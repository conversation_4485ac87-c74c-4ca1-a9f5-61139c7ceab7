# CR3加密进程扫描工具 - 项目总结

## 项目概述

本项目实现了一个基于内核驱动的CR3加密检测工具，能够检测和分析使用CR3加密技术的进程。

## 已实现的功能

### 1. CR3加密检测核心功能
- ✅ **CR3值异常检测**: 检查页对齐、范围验证、完整性验证
- ✅ **页表结构验证**: 读取和验证PML4表项
- ✅ **进程级CR3检测**: 针对特定进程的CR3加密检测

### 2. 内存保护检测
- ✅ **PE头访问测试**: 检测PE头的可读性
- ✅ **代码段保护检测**: 验证代码段访问权限
- ✅ **数据段保护检测**: 检查数据段保护状态

### 3. 隐藏进程扫描
- ✅ **PID暴力扫描**: 通过PID范围扫描发现隐藏进程
- ✅ **EPROCESS链表对比**: 对比内核链表和API结果
- ✅ **物理内存扫描**: 在物理内存中查找EPROCESS结构

### 4. 反调试保护检测
- ✅ **PEB标志检查**: 检测BeingDebugged标志
- ✅ **NtGlobalFlag检测**: 识别调试相关标志
- ✅ **进程级反调试检测**: 针对特定进程的反调试检测

### 5. 系统级扫描功能
- ✅ **全面进程扫描**: 综合所有检测方法
- ✅ **物理内存模式扫描**: 扫描物理内存中的进程签名
- ✅ **CR3值批量验证**: 验证所有进程的CR3值
- ✅ **已知保护工具检测**: 识别常见的代码保护工具

## 文件结构

```
wnbios_poc/
├── drv.h                    # 驱动接口头文件（包含CR3检测函数声明）
├── drv.cpp                  # 驱动实现文件（包含CR3检测实现）
├── cr3_scanner.cpp          # 主程序文件（交互式菜单）
├── test_cr3_detection.cpp   # 测试程序文件
├── CR3Scanner.vcxproj       # Visual Studio项目文件
├── CR3Scanner.sln           # Visual Studio解决方案文件
├── wnbios_poc.vcxproj       # 原始项目文件（已更新库链接）
├── 编译测试.bat              # 命令行编译测试脚本
├── VS编译说明.md            # Visual Studio编译详细说明
├── CR3_SCANNER_README.md    # 工具使用说明
├── usage_examples.md        # 使用示例
└── 项目总结.md              # 本文件
```

## 核心技术实现

### 1. CR3加密检测算法

```cpp
bool eneio_lib::detect_cr3_encryption(uintptr_t process_kprocess)
{
    // 1. 页对齐检查 (低12位必须为0)
    // 2. 范围验证 (合理的物理内存范围)
    // 3. 零值检查 (CR3不应为0)
    // 4. 页表读取验证 (尝试读取PML4表)
    // 5. 表项有效性检查 (Present位验证)
}
```

### 2. 隐藏进程检测机制

```cpp
void eneio_lib::detect_hidden_processes()
{
    // 1. PID暴力扫描 (4-65536)
    // 2. EPROCESS链表验证
    // 3. API调用对比
    // 4. 物理内存扫描
}
```

### 3. 物理内存扫描

```cpp
void eneio_lib::scan_physical_memory_patterns()
{
    // 1. 物理内存映射
    // 2. EPROCESS签名匹配
    // 3. 结构完整性验证
    // 4. CR3加密检测
}
```

## 编译方法

### 方法1: Visual Studio（推荐）
1. 双击 `CR3Scanner.sln`
2. 选择 Release | x64
3. 按 Ctrl+Shift+B 编译

### 方法2: 命令行
1. 运行 `编译测试.bat`
2. 自动检测VS环境并编译

### 方法3: 手动命令行
```cmd
cl.exe /EHsc /std:c++17 /D_CRT_SECURE_NO_WARNINGS ^
    cr3_scanner.cpp drv.cpp ^
    /Fe:CR3Scanner.exe ^
    /link ntdll.lib advapi32.lib kernel32.lib user32.lib
```

## 使用方法

### 基本使用
```cmd
# 以管理员权限运行
CR3Scanner.exe

# 选择功能菜单
1. 扫描指定进程的CR3加密
2. 检测内存保护机制
3. 扫描隐藏进程
...
```

### 编程接口
```cpp
eneio_lib driver;

// 检测特定进程
bool encrypted = driver.detect_cr3_encryption_for_process("notepad.exe");

// 扫描隐藏进程
driver.detect_hidden_processes();

// 全面扫描
driver.comprehensive_process_scan();
```

## 技术特点

### 优势
- ✅ **内核级访问**: 基于内核驱动，绕过用户态限制
- ✅ **多层检测**: 结合多种检测方法提高准确性
- ✅ **物理内存访问**: 直接访问物理内存，难以被绕过
- ✅ **实时检测**: 支持实时监控和检测
- ✅ **易于使用**: 提供友好的交互界面

### 局限性
- ⚠️ **需要管理员权限**: 必须以管理员身份运行
- ⚠️ **可能被杀毒软件拦截**: 内核驱动可能触发安全软件
- ⚠️ **系统兼容性**: 主要支持Windows 10/11 x64
- ⚠️ **检测绕过**: 高级保护可能使用更复杂的隐藏技术

## 安全注意事项

### 使用警告
- 🔒 **仅用于安全研究**: 不得用于恶意目的
- 🔒 **测试环境**: 建议在虚拟机中测试
- 🔒 **备份数据**: 使用前备份重要数据
- 🔒 **合法使用**: 遵守当地法律法规

### 技术风险
- 内核驱动可能影响系统稳定性
- 物理内存扫描可能耗时较长
- 某些检测可能被高级保护绕过

## 后续改进方向

### 功能增强
- [ ] 支持更多操作系统版本
- [ ] 增加更多保护工具签名
- [ ] 实现自动化报告生成
- [ ] 添加网络通信检测

### 技术优化
- [ ] 优化物理内存扫描性能
- [ ] 增强EPROCESS结构识别
- [ ] 改进CR3加密检测算法
- [ ] 添加更多反绕过技术

### 用户体验
- [ ] 图形化界面
- [ ] 配置文件支持
- [ ] 日志记录功能
- [ ] 多语言支持

## 贡献指南

如需改进或扩展功能：

1. **代码规范**: 遵循现有代码风格
2. **测试验证**: 充分测试新功能
3. **文档更新**: 更新相关文档
4. **安全考虑**: 确保不引入安全风险

## 免责声明

本工具仅用于教育和安全研究目的。使用者需要：
- 遵守当地法律法规
- 不得用于非法用途
- 承担使用风险
- 尊重他人隐私和权益

开发者不承担任何使用此工具造成的直接或间接后果。
