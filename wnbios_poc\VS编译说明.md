# Visual Studio 编译说明

## 使用Visual Studio编译CR3扫描器

### 方法1: 使用解决方案文件（推荐）

1. **打开解决方案**
   - 双击 `CR3Scanner.sln` 文件
   - 或者在Visual Studio中选择 "文件" -> "打开" -> "项目/解决方案"，然后选择 `CR3Scanner.sln`

2. **选择配置**
   - 在工具栏中选择 "Release" 配置
   - 选择 "x64" 平台

3. **编译项目**
   - 按 `Ctrl+Shift+B` 或选择 "生成" -> "生成解决方案"
   - 编译成功后，可执行文件将在 `x64\Release\` 目录中

### 方法2: 修改现有项目

如果您想在现有的 `wnbios_poc.vcxproj` 项目中编译：

1. **打开项目**
   - 双击 `wnbios_poc.vcxproj` 文件

2. **修改启动项目**
   - 右键点击解决方案资源管理器中的项目
   - 选择 "属性"
   - 在 "配置属性" -> "调试" 中
   - 将 "命令" 设置为 `$(TargetPath)`

3. **禁用CLR支持**（如果遇到编译问题）
   - 在项目属性中，找到 "配置属性" -> "高级"
   - 将 "公共语言运行时支持" 设置为 "无公共语言运行时支持"

### 编译要求

- **Visual Studio 2019 或更高版本**
- **Windows 10 SDK**
- **MSVC v143 工具集**

### 编译配置

项目已配置以下设置：
- **平台**: x64 (64位)
- **字符集**: MultiByte
- **C++标准**: C++17
- **运行时库**: MultiThreaded (Release) / MultiThreadedDebug (Debug)
- **管理员权限**: 自动请求管理员权限

### 链接的库文件

项目自动链接以下库：
- `ntdll.lib` - NT内核函数
- `advapi32.lib` - 高级Windows API（服务管理）
- `kernel32.lib` - 内核函数
- `user32.lib` - 用户界面函数

### 编译输出

编译成功后会生成：
- `CR3Scanner.exe` - 主程序
- `CR3Scanner.pdb` - 调试符号文件（Debug配置）

### 运行要求

编译后的程序需要：
- **管理员权限运行**
- **Windows 10/11 x64系统**
- **关闭杀毒软件**（可能会误报）

### 故障排除

#### 编译错误解决方案

1. **LNK2019 错误（无法解析的外部符号）**
   ```
   解决方案: 确保项目属性中已添加所需的库文件
   检查: 配置属性 -> 链接器 -> 输入 -> 附加依赖项
   ```

2. **C1189 错误（#error指令）**
   ```
   解决方案: 在预处理器定义中添加 _CRT_SECURE_NO_WARNINGS
   位置: 配置属性 -> C/C++ -> 预处理器 -> 预处理器定义
   ```

3. **CLR 相关错误**
   ```
   解决方案: 禁用公共语言运行时支持
   位置: 配置属性 -> 高级 -> 公共语言运行时支持 -> 无
   ```

#### 运行时错误解决方案

1. **驱动加载失败**
   ```
   确保以管理员权限运行
   检查Windows驱动签名策略
   ```

2. **设备句柄打开失败**
   ```
   确保没有其他程序占用驱动
   重启后重试
   ```

### 调试配置

如果需要调试程序：

1. **设置断点**
   - 在代码行左侧点击设置断点

2. **启动调试**
   - 按 `F5` 开始调试
   - 或选择 "调试" -> "开始调试"

3. **调试权限**
   - 确保Visual Studio以管理员权限运行
   - 或在项目属性中设置调试权限

### 发布配置

发布程序时建议：

1. **使用Release配置**
   - 优化代码性能
   - 减小文件大小

2. **静态链接运行时**
   - 避免依赖外部运行时库
   - 提高兼容性

3. **代码签名**（可选）
   - 减少杀毒软件误报
   - 提高用户信任度

## 快速开始

1. 双击 `CR3Scanner.sln`
2. 选择 Release | x64
3. 按 Ctrl+Shift+B 编译
4. 以管理员权限运行生成的 `CR3Scanner.exe`

编译成功后，您就可以使用CR3加密检测工具了！
