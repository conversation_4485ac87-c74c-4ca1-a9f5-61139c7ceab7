@echo off
echo Compiling CR3 Scanner with required libraries...

:: 直接使用cl编译，链接所有必要的库
cl.exe /EHsc /std:c++17 /D_CRT_SECURE_NO_WARNINGS cr3_scanner.cpp drv.cpp ^
    /Fe:cr3_scanner.exe ^
    /link ^
    ntdll.lib ^
    advapi32.lib ^
    kernel32.lib ^
    user32.lib ^
    /SUBSYSTEM:CONSOLE

if errorlevel 1 (
    echo.
    echo Compilation failed. Trying alternative method...
    echo.
    
    :: 尝试使用g++编译（如果安装了MinGW）
    g++ -std=c++17 -O2 cr3_scanner.cpp drv.cpp -o cr3_scanner.exe -lntdll -ladvapi32 -lkernel32 -luser32
    
    if errorlevel 1 (
        echo.
        echo Both compilation methods failed.
        echo Please ensure you have Visual Studio or MinGW installed.
        echo.
        pause
        exit /b 1
    ) else (
        echo.
        echo Compilation successful with g++!
    )
) else (
    echo.
    echo Compilation successful with MSVC!
)

echo.
echo Build complete. Run cr3_scanner.exe as Administrator.
echo.
pause
