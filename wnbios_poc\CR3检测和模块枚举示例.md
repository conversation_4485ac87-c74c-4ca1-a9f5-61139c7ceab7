# CR3检测和自动模块枚举功能

## 功能概述

现在当您选择"扫描指定进程的CR3加密"时，工具会自动执行以下操作：

1. **检测进程CR3值**
2. **分析CR3是否加密**
3. **自动枚举进程模块**
4. **测试内存访问**

## 使用示例

### 输入
```
请选择功能 (0-10): 1
请输入进程名 (例如: notepad.exe): DeltaForceClie
```

### 预期输出
```
=== 扫描进程 DeltaForceClie 的CR3加密 + 自动枚举模块 ===
[*] Checking CR3 encryption for process: DeltaForceClie
[+] Found target process: DeltaForceClie
[*] Process CR3: 0x344e5a000

[*] Analyzing CR3 value: 0x344e5a000
[+] CR3 appears normal: 0x344e5a000

=== 自动枚举进程模块 ===
[*] Enumerating modules for process: DeltaForceClie
[+] Process base address: 0x7ff6a2b40000
[+] PEB address: 0x12345678000
[+] LDR: 0x7ffdf2a3c4c0
[+] Module list: 0x7ffdf2a3c4d0

[*] Starting module enumeration...
[+] Module 0: DeltaForceClie.exe
    Base: 0x7ff6a2b40000
    Size: 0x1a3000
    Entry: 0x7ff6a2b41000

[+] Module 1: ntdll.dll
    Base: 0x7ffdf2a40000
    Size: 0x1f4000
    Entry: 0x7ffdf2a40000

[+] Module 2: kernel32.dll
    Base: 0x7ffdf1920000
    Size: 0x109000
    Entry: 0x7ffdf1920000

=== 测试内存访问 ===
[*] Testing memory access for process: DeltaForceClie (CR3: 0x344e5a000)
[+] Process base address: 0x7ff6a2b40000
[*] PE Header access: SUCCESS
[+] Valid DOS header found (MZ)
[+] PE Header (first 16 bytes): 4D 5A 90 00 03 00 00 00 04 00 00 00 FF FF 00 00
[*] Code section access: SUCCESS
[+] Code section (first 16 bytes): 48 83 EC 28 E8 1B 00 00 00 48 83 C4 28 C3 CC CC

[*] Testing memory access at different offsets:
[+] Offset 0x0: accessible (value: 0x4D)
[+] Offset 0x1000: accessible (value: 0x48)
[+] Offset 0x2000: accessible (value: 0x00)
[+] Offset 0x3000: accessible (value: 0x52)

[+] 未检测到CR3加密

[*] CR3检测和模块枚举完成
```

## 功能详解

### 1. CR3值分析
- 显示进程的实际CR3值
- 检查页对齐、范围等
- 验证页表可读性

### 2. 自动模块枚举
- 遍历PEB中的模块链表
- 显示每个模块的基址、大小、入口点
- 包括主程序和所有加载的DLL

### 3. 内存访问测试
- 测试PE头的可读性
- 验证DOS头和PE签名
- 检查代码段访问
- 测试不同内存偏移的可访问性

## 检测结果解读

### 正常情况
```
[+] Valid DOS header found (MZ)
[*] PE Header access: SUCCESS
[*] Code section access: SUCCESS
```

### 可疑情况
```
[SUSPICIOUS] Invalid DOS header - possible protection/encryption
[*] PE Header access: FAILED
[*] Code section access: FAILED
```

### CR3加密特征
```
[SUSPICIOUS] CR3 not page-aligned: 0x1a2b3c123
[SUSPICIOUS] Cannot read PML4 table at CR3: 0x1a2b3c000
```

## 新增菜单选项

### 选项1: CR3检测 + 模块枚举（增强版）
- 完整的CR3分析
- 自动模块枚举
- 内存访问测试

### 选项10: 仅模块枚举
- 只枚举指定进程的模块
- 不进行CR3检测
- 适合快速查看进程模块

## 技术实现

### 核心流程
```cpp
// 1. 找到目标进程
if (_stricmp(name, process_name) == 0) {
    // 2. 获取CR3值
    uintptr_t directory_table = read_virtual_memory<uintptr_t>(current_kprocess + EP_DIRECTORYTABLE);
    
    // 3. 检测CR3加密
    bool is_encrypted = detect_cr3_encryption(current_kprocess);
    
    // 4. 自动枚举模块
    enumerate_process_modules(process_name);
    
    // 5. 测试内存访问
    test_memory_access_with_cr3(process_name, directory_table);
}
```

### 内存访问测试
```cpp
// 测试PE头
UINT8 pe_header[64] = {0};
bool pe_readable = read_virtual_memory(base, pe_header, sizeof(pe_header));

// 验证DOS头
if (pe_header[0] == 'M' && pe_header[1] == 'Z') {
    printf("[+] Valid DOS header found (MZ)\n");
}

// 测试代码段
UINT8 code_section[32] = {0};
bool code_readable = read_virtual_memory(base + 0x1000, code_section, sizeof(code_section));
```

## 使用建议

1. **首选选项1**: 获得最全面的分析结果
2. **对比分析**: 对比正常进程和可疑进程的输出
3. **关注异常**: 重点关注内存访问失败的情况
4. **模块分析**: 检查是否有异常的模块或缺失的系统DLL

## 故障排除

### 模块枚举失败
- 检查进程是否存在
- 确认进程权限
- 验证PEB地址有效性

### 内存访问失败
- 可能是进程保护机制
- 检查CR3值是否正确
- 验证虚拟地址转换

这个增强功能让您可以在一次操作中获得进程的完整分析结果！
