#include "drv.h"
#include <iostream>
#include <string>

void print_banner()
{
    printf("========================================\n");
    printf("     CR3 加密进程扫描工具 v1.0\n");
    printf("========================================\n");
    printf("[*] 基于内核驱动的CR3加密检测工具\n");
    printf("[*] 支持检测隐藏进程、CR3加密、反调试保护\n");
    printf("========================================\n\n");
}

void print_menu()
{
    printf("\n=== 功能菜单 ===\n");
    printf("1. 扫描指定进程的CR3加密\n");
    printf("2. 检测内存保护机制\n");
    printf("3. 扫描隐藏进程\n");
    printf("4. 检测反调试保护\n");
    printf("5. 全面进程扫描\n");
    printf("6. 物理内存模式扫描\n");
    printf("7. 验证所有CR3值\n");
    printf("8. 扫描已知保护工具\n");
    printf("9. 列出所有进程\n");
    printf("0. 退出\n");
    printf("请选择功能 (0-9): ");
}

int main()
{
    print_banner();
    
    try {
        printf("[*] 初始化驱动...\n");
        eneio_lib driver;
        printf("[+] 驱动初始化成功\n\n");
        
        int choice;
        std::string process_name;
        
        while (true) {
            print_menu();
            std::cin >> choice;
            
            switch (choice) {
            case 1:
                printf("请输入进程名 (例如: notepad.exe): ");
                std::cin >> process_name;
                printf("\n=== 扫描进程 %s 的CR3加密 ===\n", process_name.c_str());
                if (driver.detect_cr3_encryption_for_process(process_name.c_str())) {
                    printf("[ALERT] 检测到CR3加密!\n");
                } else {
                    printf("[+] 未检测到CR3加密\n");
                }
                break;
                
            case 2:
                printf("请输入进程名 (例如: notepad.exe): ");
                std::cin >> process_name;
                printf("\n=== 检测进程 %s 的内存保护 ===\n", process_name.c_str());
                if (driver.detect_memory_protection(process_name.c_str())) {
                    printf("[ALERT] 检测到内存保护机制!\n");
                } else {
                    printf("[+] 未检测到内存保护\n");
                }
                break;
                
            case 3:
                printf("\n=== 扫描隐藏进程 ===\n");
                driver.detect_hidden_processes();
                break;
                
            case 4:
                printf("请输入进程名 (例如: notepad.exe): ");
                std::cin >> process_name;
                printf("\n=== 检测进程 %s 的反调试保护 ===\n", process_name.c_str());
                if (driver.detect_anti_debug_protection(process_name.c_str())) {
                    printf("[ALERT] 检测到反调试保护!\n");
                } else {
                    printf("[+] 未检测到反调试保护\n");
                }
                break;
                
            case 5:
                printf("\n=== 全面进程扫描 ===\n");
                driver.comprehensive_process_scan();
                break;
                
            case 6:
                printf("\n=== 物理内存模式扫描 ===\n");
                printf("[警告] 此操作可能需要较长时间，确认继续? (y/n): ");
                char confirm;
                std::cin >> confirm;
                if (confirm == 'y' || confirm == 'Y') {
                    driver.scan_physical_memory_patterns();
                } else {
                    printf("操作已取消\n");
                }
                break;
                
            case 7:
                printf("\n=== 验证所有CR3值 ===\n");
                driver.validate_all_cr3_values();
                break;
                
            case 8:
                printf("\n=== 扫描已知保护工具 ===\n");
                if (driver.scan_for_cr3_encryption_signatures()) {
                    printf("[ALERT] 发现已知保护工具!\n");
                } else {
                    printf("[+] 未发现已知保护工具\n");
                }
                break;
                
            case 9:
                printf("\n=== 列出所有进程 ===\n");
                driver.list_running_processes();
                break;
                
            case 0:
                printf("\n[*] 正在退出...\n");
                return 0;
                
            default:
                printf("无效选择，请重新输入\n");
                break;
            }
            
            printf("\n按回车键继续...");
            std::cin.ignore();
            std::cin.get();
        }
        
    } catch (const std::exception& e) {
        printf("[-] 错误: %s\n", e.what());
        printf("[-] 请确保以管理员权限运行程序\n");
        return -1;
    }
    
    return 0;
}
