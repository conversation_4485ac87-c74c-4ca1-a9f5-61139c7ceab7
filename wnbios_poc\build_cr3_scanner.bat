@echo off
echo Building CR3 Scanner...

:: 设置Visual Studio环境
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)
if errorlevel 1 (
    echo Error: Visual Studio not found. Please install Visual Studio with C++ support.
    pause
    exit /b 1
)

:: 编译CR3扫描器
echo Compiling CR3 Scanner...
cl.exe /EHsc /std:c++17 cr3_scanner.cpp drv.cpp /Fe:cr3_scanner.exe /link ntdll.lib advapi32.lib kernel32.lib user32.lib

if errorlevel 1 (
    echo Error: Compilation failed.
    pause
    exit /b 1
)

echo Build successful! 
echo Run cr3_scanner.exe as Administrator to use the tool.
pause
