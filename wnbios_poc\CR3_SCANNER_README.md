# CR3加密进程扫描工具

## 概述
这是一个基于内核驱动的CR3加密检测工具，能够检测使用CR3加密技术隐藏的进程和内存保护机制。

## 功能特性

### 1. CR3加密检测
- 检测进程的CR3值异常
- 验证页表结构完整性
- 识别CR3值的可疑特征

### 2. 内存保护检测
- 测试进程内存区域的可访问性
- 检测PE头、代码段、数据段的保护状态
- 识别内存访问阻塞

### 3. 隐藏进程扫描
- 通过PID暴力扫描发现隐藏进程
- 对比EPROCESS链表和系统API结果
- 检测进程隐藏技术

### 4. 反调试保护检测
- 检查PEB中的调试标志
- 验证NtGlobalFlag设置
- 识别常见反调试技术

### 5. 物理内存扫描
- 扫描物理内存中的EPROCESS结构
- 查找被隐藏的进程结构
- 分析内存中的进程签名

### 6. 已知保护工具检测
- 识别常见的代码保护工具
- 检测VMProtect、Themida等工具
- 分析保护工具的CR3加密实现

## 使用方法

### 编译
1. 确保安装了Visual Studio 2019或2022
2. 以管理员权限运行 `build_cr3_scanner.bat`
3. 编译成功后会生成 `cr3_scanner.exe`

### 运行
1. **必须以管理员权限运行**
2. 运行 `cr3_scanner.exe`
3. 根据菜单选择相应功能

### 功能菜单
```
1. 扫描指定进程的CR3加密    - 检测特定进程的CR3加密
2. 检测内存保护机制        - 测试进程内存访问保护
3. 扫描隐藏进程           - 发现被隐藏的进程
4. 检测反调试保护         - 识别反调试技术
5. 全面进程扫描           - 执行所有检测功能
6. 物理内存模式扫描       - 扫描物理内存结构
7. 验证所有CR3值         - 检查所有进程的CR3
8. 扫描已知保护工具       - 检测常见保护软件
9. 列出所有进程           - 显示系统进程列表
0. 退出                  - 退出程序
```

## 检测原理

### CR3加密检测
1. **页对齐检查**: CR3值必须是4KB对齐的
2. **范围验证**: CR3值应在合理的物理内存范围内
3. **页表验证**: 尝试读取和验证页表结构
4. **完整性检查**: 验证PML4表项的有效性

### 隐藏进程检测
1. **EPROCESS遍历**: 遍历内核进程链表
2. **PID暴力扫描**: 通过API验证PID存在性
3. **物理内存扫描**: 在物理内存中查找EPROCESS结构
4. **签名匹配**: 识别EPROCESS结构特征

### 内存保护检测
1. **PE头访问**: 测试进程PE头的可读性
2. **代码段访问**: 验证代码段的访问权限
3. **数据段访问**: 检查数据段的保护状态
4. **访问模式分析**: 分析内存访问模式异常

## 注意事项

### 系统要求
- Windows 10/11 x64
- 管理员权限
- Visual Studio 2019/2022 (编译时)

### 安全警告
- 此工具使用内核驱动，可能被杀毒软件报警
- 仅用于安全研究和合法的系统分析
- 不要在生产环境中使用

### 使用限制
- 物理内存扫描可能需要较长时间
- 某些检测可能影响系统稳定性
- 建议在虚拟机中测试

## 技术细节

### 驱动实现
- 基于eneio64.sys内核驱动
- 支持物理内存读写
- 实现虚拟地址转换

### 检测算法
- 多层次检测机制
- 启发式分析方法
- 签名匹配技术

### 输出格式
- 详细的检测日志
- 可疑行为标记
- 结构化的分析结果

## 故障排除

### 常见问题
1. **驱动加载失败**: 确保以管理员权限运行
2. **编译错误**: 检查Visual Studio安装
3. **检测失败**: 验证目标进程是否存在
4. **权限不足**: 确保具有SeDebugPrivilege权限

### 调试信息
程序会输出详细的调试信息，包括：
- 驱动初始化状态
- 内存访问结果
- CR3值分析
- 检测过程日志

## 免责声明
此工具仅用于教育和安全研究目的。使用者需要遵守当地法律法规，不得用于非法用途。开发者不承担任何使用此工具造成的后果。
