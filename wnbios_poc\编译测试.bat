@echo off
echo 测试Visual Studio编译环境...

:: 检查是否有Visual Studio
where cl.exe >nul 2>&1
if errorlevel 1 (
    echo 未找到cl.exe，尝试设置Visual Studio环境...
    
    :: 尝试不同版本的Visual Studio
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
        echo 已设置VS2022环境
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
        echo 已设置VS2022 Professional环境
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
        echo 已设置VS2019环境
    ) else (
        echo 错误: 未找到Visual Studio安装
        echo 请安装Visual Studio 2019或2022，并确保包含C++工具
        pause
        exit /b 1
    )
)

echo.
echo 编译器信息:
cl.exe 2>&1 | findstr "Microsoft"

echo.
echo 开始编译测试...
echo.

:: 编译CR3扫描器
echo 正在编译 CR3Scanner...
cl.exe /nologo /EHsc /std:c++17 /D_CRT_SECURE_NO_WARNINGS ^
    cr3_scanner.cpp drv.cpp ^
    /Fe:CR3Scanner.exe ^
    /link ^
    ntdll.lib advapi32.lib kernel32.lib user32.lib ^
    /SUBSYSTEM:CONSOLE ^
    /MANIFESTUAC:level=requireAdministrator

if errorlevel 1 (
    echo.
    echo 编译失败！
    echo.
    echo 可能的解决方案:
    echo 1. 确保安装了Windows 10 SDK
    echo 2. 检查Visual Studio C++工具是否完整安装
    echo 3. 尝试在Visual Studio中打开CR3Scanner.sln进行编译
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo 编译成功！
    echo 生成文件: CR3Scanner.exe
    echo.
    echo 使用说明:
    echo 1. 必须以管理员权限运行 CR3Scanner.exe
    echo 2. 首次运行可能被杀毒软件拦截，请添加信任
    echo 3. 如果遇到驱动加载问题，请重启后重试
    echo.
)

:: 清理临时文件
if exist "*.obj" del *.obj
if exist "*.pdb" del *.pdb

echo 编译测试完成！
pause
