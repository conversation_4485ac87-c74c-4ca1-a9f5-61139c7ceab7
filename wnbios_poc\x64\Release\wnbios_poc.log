﻿  drv.cpp
  MemoryReaderGUI.cpp
C:\Users\<USER>\Desktop\驱动漏洞\vstest\wnbios_poc-main\wnbios_poc\MemoryReaderGUI.cpp(859,94): warning C4267: “参数”: 从“size_t”转换到“unsigned long”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: “=”: 从“wchar_t”转换到“char”，可能丢失数据
  (编译源文件“MemoryReaderGUI.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\驱动漏洞\vstest\wnbios_poc-main\wnbios_poc\MemoryReaderGUI.cpp(518,43):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)”的引用
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\Desktop\驱动漏洞\vstest\wnbios_poc-main\wnbios_poc\MemoryReaderGUI.cpp(518,43):
              请参阅 "MemoryReaderForm::RefreshProcessList" 中对 "std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string" 的第一个引用
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          查看对正在编译的函数 模板 实例化“void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)”的引用
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          查看对正在编译的函数 模板 实例化“_OutIt std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)”的引用
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  wnbios_poc.cpp
  .NETFramework,Version=v4.7.2.AssemblyAttributes.cpp
  正在生成代码...
  LINK : 已指定 /LTCG，但不需要生成代码；从链接命令行中移除 /LTCG 以提高链接器性能
  wnbios_poc.vcxproj -> C:\Users\<USER>\Desktop\驱动漏洞\vstest\wnbios_poc-main\x64\Release\wnbios_poc.exe
