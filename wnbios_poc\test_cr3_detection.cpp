#include "drv.h"
#include <iostream>

// 简单的测试程序，用于验证CR3加密检测功能
int main()
{
    printf("=== CR3加密检测功能测试 ===\n\n");
    
    try {
        printf("[*] 初始化驱动...\n");
        eneio_lib driver;
        printf("[+] 驱动初始化成功\n\n");
        
        // 测试1: 检测系统进程
        printf("测试1: 检测系统进程 (System)\n");
        printf("----------------------------------------\n");
        if (driver.detect_cr3_encryption_for_process("System")) {
            printf("[结果] System进程检测到CR3加密\n");
        } else {
            printf("[结果] System进程未检测到CR3加密\n");
        }
        printf("\n");
        
        // 测试2: 检测explorer进程
        printf("测试2: 检测explorer进程\n");
        printf("----------------------------------------\n");
        if (driver.detect_cr3_encryption_for_process("explorer.exe")) {
            printf("[结果] explorer.exe检测到CR3加密\n");
        } else {
            printf("[结果] explorer.exe未检测到CR3加密\n");
        }
        printf("\n");
        
        // 测试3: 检测内存保护
        printf("测试3: 检测explorer.exe的内存保护\n");
        printf("----------------------------------------\n");
        if (driver.detect_memory_protection("explorer.exe")) {
            printf("[结果] 检测到内存保护机制\n");
        } else {
            printf("[结果] 未检测到内存保护机制\n");
        }
        printf("\n");
        
        // 测试4: 反调试检测
        printf("测试4: 检测explorer.exe的反调试保护\n");
        printf("----------------------------------------\n");
        if (driver.detect_anti_debug_protection("explorer.exe")) {
            printf("[结果] 检测到反调试保护\n");
        } else {
            printf("[结果] 未检测到反调试保护\n");
        }
        printf("\n");
        
        // 测试5: 扫描已知保护工具
        printf("测试5: 扫描已知保护工具\n");
        printf("----------------------------------------\n");
        if (driver.scan_for_cr3_encryption_signatures()) {
            printf("[结果] 发现已知保护工具\n");
        } else {
            printf("[结果] 未发现已知保护工具\n");
        }
        printf("\n");
        
        // 测试6: 列出前10个进程
        printf("测试6: 列出系统进程\n");
        printf("----------------------------------------\n");
        driver.list_running_processes();
        printf("\n");
        
        printf("=== 所有测试完成 ===\n");
        
    } catch (const std::exception& e) {
        printf("[-] 测试失败: %s\n", e.what());
        printf("[-] 请确保以管理员权限运行程序\n");
        return -1;
    }
    
    printf("\n按回车键退出...");
    std::cin.get();
    return 0;
}
