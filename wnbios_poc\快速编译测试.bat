@echo off
echo 快速编译测试...

:: 检查编译器
where cl.exe >nul 2>&1
if errorlevel 1 (
    echo 设置Visual Studio环境...
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul
    ) else (
        echo 错误: 未找到Visual Studio
        pause
        exit /b 1
    )
)

echo 编译CR3Scanner...
cl.exe /nologo /EHsc /std:c++17 /D_CRT_SECURE_NO_WARNINGS ^
    cr3_scanner.cpp drv.cpp ^
    /Fe:CR3Scanner.exe ^
    /link ^
    ntdll.lib advapi32.lib kernel32.lib user32.lib ^
    /SUBSYSTEM:CONSOLE

if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
) else (
    echo 编译成功！
    echo.
    echo 新功能说明:
    echo - 选项1: CR3检测 + 自动模块枚举
    echo - 选项10: 仅模块枚举
    echo.
    echo 以管理员权限运行 CR3Scanner.exe 来测试
)

:: 清理
if exist "*.obj" del *.obj >nul 2>&1

pause
