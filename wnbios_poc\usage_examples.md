# CR3加密检测工具使用示例

## 基本使用示例

### 1. 检测特定进程的CR3加密

```cpp
#include "drv.h"

int main() {
    try {
        eneio_lib driver;
        
        // 检测notepad.exe是否使用CR3加密
        if (driver.detect_cr3_encryption_for_process("notepad.exe")) {
            printf("[ALERT] notepad.exe使用了CR3加密!\n");
        } else {
            printf("[+] notepad.exe未使用CR3加密\n");
        }
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
    }
    return 0;
}
```

### 2. 扫描隐藏进程

```cpp
#include "drv.h"

int main() {
    try {
        eneio_lib driver;
        
        // 扫描系统中的隐藏进程
        printf("开始扫描隐藏进程...\n");
        driver.detect_hidden_processes();
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
    }
    return 0;
}
```

### 3. 全面系统扫描

```cpp
#include "drv.h"

int main() {
    try {
        eneio_lib driver;
        
        // 执行全面的系统扫描
        printf("开始全面系统扫描...\n");
        driver.comprehensive_process_scan();
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
    }
    return 0;
}
```

## 高级使用示例

### 1. 批量进程检测

```cpp
#include "drv.h"
#include <vector>
#include <string>

int main() {
    try {
        eneio_lib driver;
        
        // 要检测的进程列表
        std::vector<std::string> processes = {
            "explorer.exe",
            "chrome.exe", 
            "notepad.exe",
            "calc.exe",
            "cmd.exe"
        };
        
        printf("批量检测进程CR3加密状态:\n");
        printf("================================\n");
        
        for (const auto& proc : processes) {
            printf("检测进程: %s\n", proc.c_str());
            
            bool has_cr3_encryption = driver.detect_cr3_encryption_for_process(proc.c_str());
            bool has_memory_protection = driver.detect_memory_protection(proc.c_str());
            bool has_anti_debug = driver.detect_anti_debug_protection(proc.c_str());
            
            printf("  CR3加密: %s\n", has_cr3_encryption ? "是" : "否");
            printf("  内存保护: %s\n", has_memory_protection ? "是" : "否");
            printf("  反调试: %s\n", has_anti_debug ? "是" : "否");
            printf("--------------------------------\n");
        }
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
    }
    return 0;
}
```

### 2. 持续监控模式

```cpp
#include "drv.h"
#include <chrono>
#include <thread>

int main() {
    try {
        eneio_lib driver;
        
        printf("启动持续监控模式 (每30秒扫描一次)\n");
        printf("按Ctrl+C退出...\n\n");
        
        int scan_count = 0;
        while (true) {
            scan_count++;
            printf("=== 第%d次扫描 ===\n", scan_count);
            
            // 检测隐藏进程
            driver.detect_hidden_processes();
            
            // 验证所有CR3值
            driver.validate_all_cr3_values();
            
            // 扫描已知保护工具
            driver.scan_for_cr3_encryption_signatures();
            
            printf("扫描完成，等待30秒...\n\n");
            std::this_thread::sleep_for(std::chrono::seconds(30));
        }
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
    }
    return 0;
}
```

## 命令行参数示例

### 创建支持命令行参数的版本

```cpp
#include "drv.h"
#include <iostream>
#include <string>

void print_usage(const char* program_name) {
    printf("用法: %s [选项] [进程名]\n", program_name);
    printf("选项:\n");
    printf("  -c, --cr3          检测CR3加密\n");
    printf("  -m, --memory       检测内存保护\n");
    printf("  -a, --anti-debug   检测反调试保护\n");
    printf("  -h, --hidden       扫描隐藏进程\n");
    printf("  -s, --scan         全面扫描\n");
    printf("  -l, --list         列出所有进程\n");
    printf("  --help             显示此帮助信息\n");
    printf("\n示例:\n");
    printf("  %s -c notepad.exe     # 检测notepad.exe的CR3加密\n", program_name);
    printf("  %s -h                 # 扫描隐藏进程\n", program_name);
    printf("  %s -s                 # 全面系统扫描\n", program_name);
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }
    
    try {
        eneio_lib driver;
        
        std::string option = argv[1];
        std::string process_name = (argc > 2) ? argv[2] : "";
        
        if (option == "-c" || option == "--cr3") {
            if (process_name.empty()) {
                printf("错误: 需要指定进程名\n");
                return 1;
            }
            if (driver.detect_cr3_encryption_for_process(process_name.c_str())) {
                printf("[ALERT] %s 使用了CR3加密!\n", process_name.c_str());
            } else {
                printf("[+] %s 未使用CR3加密\n", process_name.c_str());
            }
        }
        else if (option == "-m" || option == "--memory") {
            if (process_name.empty()) {
                printf("错误: 需要指定进程名\n");
                return 1;
            }
            if (driver.detect_memory_protection(process_name.c_str())) {
                printf("[ALERT] %s 使用了内存保护!\n", process_name.c_str());
            } else {
                printf("[+] %s 未使用内存保护\n", process_name.c_str());
            }
        }
        else if (option == "-a" || option == "--anti-debug") {
            if (process_name.empty()) {
                printf("错误: 需要指定进程名\n");
                return 1;
            }
            if (driver.detect_anti_debug_protection(process_name.c_str())) {
                printf("[ALERT] %s 使用了反调试保护!\n", process_name.c_str());
            } else {
                printf("[+] %s 未使用反调试保护\n", process_name.c_str());
            }
        }
        else if (option == "-h" || option == "--hidden") {
            driver.detect_hidden_processes();
        }
        else if (option == "-s" || option == "--scan") {
            driver.comprehensive_process_scan();
        }
        else if (option == "-l" || option == "--list") {
            driver.list_running_processes();
        }
        else if (option == "--help") {
            print_usage(argv[0]);
        }
        else {
            printf("错误: 未知选项 %s\n", option.c_str());
            print_usage(argv[0]);
            return 1;
        }
        
    } catch (const std::exception& e) {
        printf("错误: %s\n", e.what());
        printf("请确保以管理员权限运行程序\n");
        return -1;
    }
    
    return 0;
}
```

## 输出示例

### 正常进程检测输出
```
[*] Checking CR3 encryption for process: explorer.exe
[+] Found target process: explorer.exe
[*] Analyzing CR3 value: 0x1a2b3c000
[+] CR3 appears normal: 0x1a2b3c000
[+] 未检测到CR3加密
```

### 检测到CR3加密的输出
```
[*] Checking CR3 encryption for process: protected.exe
[+] Found target process: protected.exe
[*] Analyzing CR3 value: 0x1a2b3c123
[SUSPICIOUS] CR3 not page-aligned: 0x1a2b3c123
[ALERT] 检测到CR3加密!
```

### 隐藏进程扫描输出
```
[*] Scanning for hidden processes...
[HIDDEN] Found hidden process PID: 1234
[HIDDEN] Found hidden process PID: 5678
[*] Hidden process scan complete. Found 2 hidden processes
```

## 注意事项

1. **管理员权限**: 所有示例都需要管理员权限运行
2. **异常处理**: 建议总是使用try-catch包装驱动操作
3. **资源清理**: eneio_lib析构函数会自动清理资源
4. **性能考虑**: 物理内存扫描可能耗时较长，建议谨慎使用
